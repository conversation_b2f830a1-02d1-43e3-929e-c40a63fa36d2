<template>
  <div v-if="show" class="analyze-modal-overlay" @click.self="close">
    <div class="analyze-modal relative">
      <button class="modal-close-btn" @click="close">
        <img src="~/assets/image/close-middle.svg" alt="Close" class="w-5 h-5 btn-icon-dark" />
        <img src="~/assets/image/Close-small.svg" alt="Close" class="w-6 h-6 btn-icon-light" />
      </button>

      <h3>Analyze</h3>
      <div class="analyze-buttons">
        <button
          @click="handleGitHub"
          :disabled="!isGitHubEnabled"
          :class="{ 'btn-disabled': !isGitHubEnabled }"
        >
          <img src="~/assets/image/github2.svg" alt="" class="w-6 h-6 btn-icon-light"/>
          <img src="~/assets/image/github3.svg" alt="" class="w-6 h-6 btn-icon-dark" />
          <span> GitHub </span>
        </button>
        <button
          @click="handleScholar"
          :disabled="!isScholarEnabled"
          :class="{ 'btn-disabled': !isScholarEnabled }"
        >
          <img src="~/assets/image/mortarboard1.svg" alt="" class="btn-icon-light"/>
          <img src="~/assets/image/mortarboard2.svg" alt=""  class="btn-icon-dark"/>
          <span> Google Scholar </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { defineProps, defineEmits, computed } from 'vue'

  const props = defineProps({
    show: Boolean,
    candidate: Object,
  })

  const emit = defineEmits(['update:show', 'analyze'])

  // Computed properties to determine if buttons should be enabled
  const isGitHubEnabled = computed(() => {
    return props.candidate?.profile?.github != null
  })

  const isScholarEnabled = computed(() => {
    return props.candidate?.profile?.scholar != null
  })

  function handleGitHub() {
    // Only proceed if GitHub data is available
    if (!isGitHubEnabled.value) return

    const githubId = props.candidate.profile.github
    const encodedId = encodeURIComponent(githubId)
    const url = `/github?user=${encodedId}`

    // Open in new tab
    window.open(url, '_blank')
    emit('update:show', false)
  }

  function handleScholar() {
    // Only proceed if Scholar data is available
    if (!isScholarEnabled.value) return

    const scholarId = props.candidate.profile.scholar
    const encodedId = encodeURIComponent(scholarId)
    const url = `/scholar?user=${encodedId}`

    // Open in new tab
    window.open(url, '_blank')
    emit('update:show', false)
  }

  function close() {
    emit('update:show', false)
  }
</script>

<style scoped>
  .analyze-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  }

  .analyze-modal {
    background: white;
    padding: 20px;
    border-radius: 8px;
    width: 360px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .dark .analyze-modal {
    background: #1e1e1e;
    color: white;
  }

  .analyze-buttons {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .analyze-buttons button {
    padding: 8px;
    font-size: 14px;
    border: 1px solid #000;
    border-radius: 4px;
    cursor: pointer;
    background-color: #fff;
    color: black;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .dark .analyze-buttons button {
    background-color: #181818;
    border-radius: 4px;
    border: 1px solid #27272a;
    color: white;
  }

  .analyze-buttons button:hover {
    background-color: #ccc;
  }

  .dark .analyze-buttons button:hover {
    background-color: #27272a;
  }

  .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f5f5f5 !important;
    color: #999 !important;
  }

  .btn-disabled:hover {
    background-color: #f5f5f5 !important;
  }

  .dark .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #2a2a2a !important;
    color: #666 !important;
    border-color: #444 !important;
  }

  .dark .btn-disabled:hover {
    background-color: #2a2a2a !important;
  }

  .modal-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 4px;
    transition: opacity 0.3s ease;
  }

  .modal-close-btn:hover {
    opacity: 0.7;
  }

    /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }
</style>
